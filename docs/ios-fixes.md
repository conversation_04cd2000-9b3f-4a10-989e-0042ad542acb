# iOS Safari 兼容性修复说明

## 问题描述

在iOS Safari上，聊天页面出现以下问题：
1. 预设问题不显示
2. 聊天内容无法滚动
3. 键盘弹起时布局异常

## 修复内容

### 1. 创建iOS兼容性工具 (`src/utils/iosCompatibility.ts`)

- **设备检测**: 检测iOS设备和Safari浏览器
- **滚动修复**: 添加`-webkit-overflow-scrolling: touch`和`translateZ(0)`
- **层级修复**: 修复iOS Safari的层级显示问题
- **视口修复**: 使用CSS变量动态设置视口高度
- **键盘适配**: 监听Visual Viewport API和resize事件
- **毛玻璃效果**: 添加`-webkit-backdrop-filter`兼容性

### 2. 聊天页面修复 (`src/pages/Chat/chat.vue`)

#### 样式修复
```scss
// iOS键盘适配的全局样式
@supports (-webkit-touch-callout: none) {
  .v-chat-container {
    // iOS设备特殊处理
    height: calc(var(--vh, 1vh) * 100); // 使用CSS变量修复视口高度
    -webkit-overflow-scrolling: touch;
    overflow: hidden;

    .v-chat {
      // 修复iOS Safari中的滚动和显示问题
      -webkit-transform: translateZ(0);
      transform: translateZ(0);

      .chat-content {
        -webkit-overflow-scrolling: touch;
        
        .chat-scroll-wrapper {
          // 修复iOS Safari滚动问题
          -webkit-overflow-scrolling: touch;
          overflow-y: scroll;
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
        }
      }
    }
  }
}
```

#### 调试日志
- 添加iOS设备检测和调试信息输出
- 监听预设问题更新并输出调试信息

### 3. 预设问题组件修复 (`src/components/Chat/PresetQuestions.vue`)

#### 样式修复
```scss
.preset-questions-container {
  // iOS Safari 兼容性修复
  @supports (-webkit-touch-callout: none) {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    z-index: 10;
  }

  .preset-questions-scroll {
    // iOS Safari 滚动优化
    @supports (-webkit-touch-callout: none) {
      -webkit-overflow-scrolling: touch;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  .preset-question-item {
    // iOS Safari 兼容性修复
    @supports (-webkit-touch-callout: none) {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-backdrop-filter: blur(10px);
      background: var(--bg-glass) !important;
      border-color: var(--border-light) !important;
    }
  }
}
```

#### 功能增强
- 添加预设问题变化监听
- 增加iOS设备调试信息输出

### 4. 输入框组件修复 (`src/components/Chat/inputBar.vue`)

#### 全局样式修复
```scss
:global(body.ios-keyboard-visible) {
  @supports (-webkit-touch-callout: none) {
    // 确保预设问题在键盘弹起时仍然可见
    .preset-questions-container {
      z-index: 1002 !important;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }
}
```

### 5. 设计系统更新 (`src/styles/design-system.scss`)

添加iOS视口高度修复变量：
```scss
:root {
  // iOS视口高度修复
  --vh: 1vh;
}
```

### 6. 主入口文件修复 (`src/main.ts`)

在应用启动时应用iOS兼容性修复：
```typescript
import { applyIOSFixes, logIOSDebugInfo } from '@/utils/iosCompatibility';

// 应用iOS兼容性修复
applyIOSFixes();
logIOSDebugInfo();
```

### 7. 调试页面 (`src/pages/IOSDebug.vue`)

创建专门的iOS调试页面，用于：
- 检测设备信息
- 监控视口变化
- 测试键盘弹起
- 测试预设问题点击
- 测试滚动功能

访问路径: `/ios-debug`

## 修复原理

### 1. 滚动问题
- 使用`-webkit-overflow-scrolling: touch`启用iOS原生滚动
- 使用`transform: translateZ(0)`创建新的层叠上下文
- 明确指定`overflow-y: scroll`

### 2. 显示问题
- 使用`transform: translateZ(0)`和`will-change: transform`强制硬件加速
- 设置合适的`z-index`确保层级正确
- 使用`!important`覆盖可能的样式冲突

### 3. 视口问题
- 使用CSS变量`--vh`动态设置视口高度
- 监听`resize`和`orientationchange`事件更新视口高度
- 使用`calc(var(--vh, 1vh) * 100)`替代`100vh`

### 4. 键盘适配
- 优先使用Visual Viewport API
- 备用方案监听window resize事件
- 动态添加/移除`ios-keyboard-visible`类

## 测试建议

1. **访问调试页面**: 在iOS设备上访问`/ios-debug`查看设备信息和测试功能
2. **检查控制台**: 查看iOS相关的调试日志
3. **测试场景**:
   - 预设问题是否正常显示和点击
   - 聊天内容是否可以正常滚动
   - 键盘弹起时布局是否正常
   - 毛玻璃效果是否正常显示

## 注意事项

1. 这些修复主要针对iOS Safari，不会影响其他浏览器
2. 使用了`@supports (-webkit-touch-callout: none)`来检测iOS设备
3. 所有修复都是渐进增强，不会破坏现有功能
4. 调试信息只在iOS设备上输出，减少其他平台的日志噪音
