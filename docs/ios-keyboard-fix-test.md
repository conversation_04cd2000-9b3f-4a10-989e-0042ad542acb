# iOS键盘弹起问题修复测试指南

## 问题描述
在iOS Safari上，当键盘弹起时，整个页面被向上推动，导致：
1. 预设问题被推出视口，用户看不到
2. 聊天内容被压缩，无法正常查看
3. 页面布局混乱

## 修复方案
通过以下方式修复键盘弹起问题：
1. **防止页面整体被推上去**：不使用`position: fixed`在body上
2. **固定输入框位置**：让输入框固定在底部，不被键盘推动
3. **调整内容区域**：根据键盘高度动态调整内容区域大小
4. **保持预设问题可见**：确保预设问题始终在可视区域内

## 测试步骤

### 1. 访问调试页面
在iOS设备上访问：`/ios-debug`

检查以下信息：
- 设备类型是否正确识别为iOS
- Visual Viewport API是否支持
- 初始视口信息是否正确

### 2. 测试键盘弹起检测
1. 点击调试页面中的输入框
2. 观察键盘状态信息是否正确更新：
   - 键盘状态：弹起
   - 键盘高度：应显示实际键盘高度（通常200-350px）
   - CSS变量键盘高度：应与键盘高度一致
   - 页面是否被顶起：应显示"否"

### 3. 测试聊天页面
访问聊天页面，进行以下测试：

#### 3.1 预设问题显示测试
1. 发送一条消息，等待AI回复
2. 观察页面底部是否显示预设问题
3. 预设问题是否可以正常点击

#### 3.2 键盘弹起测试
1. 点击输入框，键盘弹起
2. 检查以下项目：
   - [ ] 预设问题是否仍然可见
   - [ ] 聊天内容是否可以正常滚动
   - [ ] 输入框是否固定在底部
   - [ ] 页面是否没有被整体推上去
   - [ ] 聊天内容区域高度是否合理调整

#### 3.3 键盘收起测试
1. 点击键盘上的"完成"按钮或点击其他区域
2. 检查以下项目：
   - [ ] 页面布局是否恢复正常
   - [ ] 预设问题是否正常显示
   - [ ] 聊天内容是否可以正常滚动

### 4. 控制台日志检查
打开Safari开发者工具，检查控制台日志：

应该看到以下类型的日志：
```
🔧 [iosCompatibility] 开始应用iOS兼容性修复
📱 [iosCompatibility] iOS设备调试信息: {...}
🎹 [iosCompatibility] iOS键盘弹起，键盘高度: 334
🎯 [chat.vue] 收到推荐问题: [...]
🎯 [PresetQuestions] 组件挂载: {...}
```

### 5. 边界情况测试

#### 5.1 横屏测试
1. 将设备旋转为横屏
2. 重复上述键盘弹起测试
3. 检查布局是否正常

#### 5.2 多次键盘弹起/收起
1. 快速多次点击输入框，让键盘反复弹起收起
2. 检查页面状态是否正确更新
3. 检查是否有内存泄漏或事件监听器问题

#### 5.3 长内容测试
1. 发送多条长消息，让聊天内容超出一屏
2. 键盘弹起时测试滚动功能
3. 检查内容是否可以正常滚动到顶部和底部

## 预期结果

### ✅ 修复后的正确行为
1. **键盘弹起时**：
   - 页面不会被整体向上推动
   - 预设问题始终可见
   - 聊天内容区域高度合理调整
   - 输入框固定在屏幕底部
   - 聊天内容可以正常滚动

2. **键盘收起时**：
   - 页面布局完全恢复
   - 所有功能正常工作

### ❌ 修复前的问题行为
1. **键盘弹起时**：
   - 整个页面被向上推动
   - 预设问题被推出视口
   - 聊天内容被压缩
   - 用户体验差

## 故障排除

### 如果预设问题仍然不显示
1. 检查控制台是否有JavaScript错误
2. 确认`onRecommendations`回调是否被正确调用
3. 检查CSS层级是否正确

### 如果键盘弹起检测不准确
1. 检查Visual Viewport API是否可用
2. 确认设备是否正确识别为iOS
3. 检查键盘高度阈值设置（当前为100px）

### 如果页面仍然被推上去
1. 检查是否有其他CSS规则干扰
2. 确认`ios-keyboard-visible`类是否正确添加/移除
3. 检查viewport meta标签设置

## 技术细节

### 关键修复点
1. **不使用body的position: fixed**：避免整个页面被固定
2. **使用CSS变量传递键盘高度**：`--keyboard-height`
3. **动态调整内容区域**：`height: calc(100% - var(--keyboard-height, 0px))`
4. **输入框固定定位**：`position: fixed; bottom: 0;`

### CSS选择器优先级
```scss
:global(body.ios-keyboard-visible) .v-chat-container {
  @supports (-webkit-touch-callout: none) {
    // iOS特定样式
  }
}
```

这个修复方案确保了只在iOS设备上生效，不会影响其他平台的正常使用。
