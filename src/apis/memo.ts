import fetchInstance from '@/lib/fetch';

// 备忘录项目接口
export interface IMemoItem {
  label: string;
  value: string;
}

// 备忘录分类接口
export interface IMemoCategory {
  title: string;
  items: IMemoItem[];
}

// 事件数据结构
interface IEventItem {
  event_id: string;
  user_id: string;
  description_text: string;
  timestamp: string;
  participants: string[];
  location: string | null;
  topics: string[];
  updated_at?: string;
  memory_type?: string;
}

// 备忘录原始响应数据结构
interface IMemoResponse {
  result: string;
  person: {
    person_id: string;
    user_id: string;
    is_user: boolean;
    canonical_name: string;
    aliases: string;
    relationships: unknown[];
    profile_summary: string;
    key_attributes: Record<string, unknown>;
    avatar: string;
    intimacy_score: number;
    intimacy_updated_at: string;
    created_at: string;
    updated_at: string;
  };
  events: {
    result: string;
    events: IEventItem[];
  };
}

// 处理嵌套对象，扁平化为键值对（只显示最内层的键值对）
const flattenObject = (obj: unknown): Record<string, string> => {
  const result: Record<string, string> = {};

  const flatten = (currentObj: unknown) => {
    if (currentObj === null || currentObj === undefined) {
      return;
    }

    if (typeof currentObj === 'string') {
      // 字符串值直接忽略，因为没有键名
      return;
    }

    if (typeof currentObj !== 'object' || Array.isArray(currentObj)) {
      // 数组或非对象类型直接忽略
      return;
    }

    // 遍历对象的所有键值对
    Object.entries(currentObj as Record<string, unknown>).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        return;
      }

      if (typeof value === 'object' && !Array.isArray(value)) {
        // 如果值是对象，递归处理
        flatten(value);
      } else if (Array.isArray(value) && value.length > 0) {
        // 如果值是数组，连接为字符串
        result[key] = value.join(', ');
      } else if (value !== '') {
        // 如果值是基本类型且不为空，直接转换为字符串
        result[key] = String(value);
      }
    });
  };

  flatten(obj);
  return result;
};

// 将扁平化的数据转换为分类结构
const transformToCategories = (flatData: Record<string, string>, events: IEventItem[]): IMemoCategory[] => {
  const categories: Record<string, IMemoItem[]> = {};

  // 处理 key_attributes 数据
  Object.entries(flatData).forEach(([key, value]) => {
    if (!value || value.trim() === '') {
      return;
    }

    // 根据键名确定分类
    let categoryName = '其他信息';

    if (
      key.includes('兴趣') ||
      key.includes('爱好') ||
      key.includes('运动') ||
      key.includes('音乐') ||
      key.includes('阅读')
    ) {
      categoryName = '兴趣爱好';
    } else if (
      key.includes('工作') ||
      key.includes('职业') ||
      key.includes('公司') ||
      key.includes('职位') ||
      key.includes('行业')
    ) {
      categoryName = '工作信息';
    } else if (
      key.includes('基本信息') ||
      key.includes('家乡') ||
      key.includes('性别') ||
      key.includes('生日') ||
      key.includes('当前城市')
    ) {
      categoryName = '基本信息';
    } else if (key.includes('家庭') || key.includes('婚育') || key.includes('配偶') || key.includes('子女')) {
      categoryName = '家庭信息';
    } else if (key.includes('联系') || key.includes('电话') || key.includes('邮箱') || key.includes('微信')) {
      categoryName = '联系方式';
    } else if (key.includes('旅游') || key.includes('历史') || key.includes('期望') || key.includes('关心')) {
      categoryName = '个人特征';
    } else if (key.includes('餐饮') || key.includes('偏好') || key.includes('喜欢')) {
      categoryName = '个人偏好';
    }

    if (!categories[categoryName]) {
      categories[categoryName] = [];
    }

    categories[categoryName].push({
      label: key,
      value,
    });
  });

  // 处理 events 数据
  if (events && events.length > 0) {
    if (!categories['重要事件']) {
      categories['重要事件'] = [];
    }

    events.forEach((event, index) => {
      if (event.description_text && event.description_text.trim() !== '') {
        // 格式化时间戳
        const formatDate = (timestamp: string) => {
          try {
            const date = new Date(timestamp);
            return date.toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            });
          } catch {
            return timestamp;
          }
        };

        categories['重要事件'].push({
          label: `${formatDate(event.timestamp)}`,
          value: event.description_text,
        });
      }
    });
  }

  // 转换为数组格式
  return Object.entries(categories).map(([title, items]) => ({
    title,
    items,
  }));
};

// 获取并处理备忘录信息
export const getMemoInfo = async (userId: string): Promise<IMemoCategory[]> => {
  try {
    console.log('🔄 [memo.ts] 开始获取备忘录数据, userId:', userId);

    // 获取原始数据
    const response: IMemoResponse = await fetchInstance.fetch('/humanrelation/get_user_profile_and_events', {
      method: 'GET',
      params: {
        user_id: userId,
      },
    });

    if (response.result !== 'success') {
      throw new Error('获取备忘录数据失败');
    }

    // 扁平化 key_attributes 数据
    const flatData = flattenObject(response.person.key_attributes);

    // 获取 events 数据
    const events = response.events?.events || [];

    // 转换为分类结构
    const categories = transformToCategories(flatData, events);

    console.log('✅ [memo.ts] 备忘录数据处理成功:', {
      person: response.person.canonical_name,
      key_attributes: flatData,
      events_count: events.length,
      categories_count: categories.length,
    });

    return categories;
  } catch (error) {
    console.error('❌ [memo.ts] 获取备忘录数据失败:', error);
    throw error;
  }
};

// 会话变化响应接口
interface IConversationChangesResponse {
  result: string;
  conversation_id: string;
  profile_updates: Record<string, string>;
  new_events: Array<{
    event_id: string;
    description_text: string;
    participants: string[];
    location: string;
    topics: string[];
    timestamp: string;
  }>;
  intimacy_increase: number;
  has_updates: boolean;
}

// 统一的更新结果接口
export interface IMemoUpdateResult {
  hasUpdate: boolean;
  data?: {
    type: 'memo' | 'knowledge';
    content: string;
  };
}

// 查询会话变化
export const getConversationChanges = async (userId: string, conversationId: string): Promise<IMemoUpdateResult> => {
  try {
    console.log('🔄 [memo.ts] 开始查询会话变化, userId:', userId, 'conversationId:', conversationId);

    // 调用后端接口
    const response: IConversationChangesResponse = await fetchInstance.fetch('/humanrelation/conversation_changes', {
      method: 'GET',
      params: {
        user_id: userId,
        conversation_id: conversationId,
      },
    });

    if (response.result !== 'success') {
      throw new Error('查询会话变化失败');
    }

    // 如果没有更新，直接返回
    if (!response.has_updates) {
      console.log('🔍 [memo.ts] 没有检测到更新');
      return { hasUpdate: false };
    }

    // 处理profile更新
    if (response.profile_updates && Object.keys(response.profile_updates).length > 0) {
      const profileEntries = Object.entries(response.profile_updates);
      const firstEntry = profileEntries[0];
      const content = `记录了您的${firstEntry[0]}为${firstEntry[1]}`;

      console.log('✅ [memo.ts] 检测到profile更新:', content);
      return {
        hasUpdate: true,
        data: {
          type: 'memo',
          content,
        },
      };
    }

    // 处理新事件
    if (response.new_events && response.new_events.length > 0) {
      const firstEvent = response.new_events[0];
      const content = firstEvent.description_text;

      console.log('✅ [memo.ts] 检测到新事件:', content);
      return {
        hasUpdate: true,
        data: {
          type: 'memo',
          content,
        },
      };
    }

    // 处理懂量提升
    if (response.intimacy_increase && response.intimacy_increase > 0) {
      const content = `懂量+${response.intimacy_increase}`;

      console.log('✅ [memo.ts] 检测到懂量提升:', content);
      return {
        hasUpdate: true,
        data: {
          type: 'knowledge',
          content,
        },
      };
    }

    console.log('🔍 [memo.ts] has_updates为true但没有具体更新内容');
    return { hasUpdate: false };
  } catch (error) {
    console.error('❌ [memo.ts] 查询会话变化失败:', error);
    // 查询失败时返回无更新，避免影响正常聊天流程
    return { hasUpdate: false };
  }
};
