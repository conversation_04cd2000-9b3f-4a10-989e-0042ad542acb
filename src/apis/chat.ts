import fetchInstance, { getAccessToken } from '@/lib/fetch';
import { fetchEventSource } from '@microsoft/fetch-event-source';

// 语音转文字
export const getStreamAsr = (params: IAsrParams): Promise<IApiResponseData<IAsrRequest>> => {
  const { data, ...audioParams } = params;
  return fetchInstance.fetch('/weiwei/stream/asr', {
    method: 'POST',
    body: data, // 直接发送二进制数据
    headers: {
      'Session-Id': params.sessionId,
      'Audio-Param': btoa(JSON.stringify(audioParams)),
      'Content-Type': 'application/octet-stream',
    },
    timeout: 10000, // 增加超时时间到30秒，测试环境有问题
  });
};
// 单条播放-文字转语音
export const getStreamSynthesis = (params: {
  text: string;
  signal?: AbortSignal; // 添加可选的 AbortSignal
}) => {
  const { text, signal } = params;
  return fetchInstance.fetch(`/weiwei/tts/stream-synthesis`, {
    method: 'POST',
    body: JSON.stringify({
      text,
      voiceName: 'tuanshushuo',
      speed: 50,
      volume: 50,
      sampleRate: 16000,
      audioFormat: 'mp3',
    }),
    responseType: 'blob',
    headers: {
      Accept: 'audio/mpeg',
    },
    signal, // 传递 AbortSignal
  });
};
// 自动播放-文字转语音
export const getTtsResponse = (params: {
  id: string;
  signal?: AbortSignal; // 添加可选的 AbortSignal
}) => {
  const { signal, id } = params;
  return fetchInstance.fetch('/weiwei/stream/get-tts-response', {
    method: 'GET',
    headers: {
      'tts-session-id': id,
      Accept: 'audio/mpeg',
    },
    responseType: 'blob',
    signal, // 传递 AbortSignal
  });
};
// 话题推荐
export const getTopicRecommend = (): Promise<IApiResponseData<{ list: ITopicRecommend[] }>> => {
  return fetchInstance.fetch('/weiwei/topic/recommend', {
    method: 'GET',
  });
};

// 工具调用数据接口
export interface IToolCall {
  tool_name: string;
  tool_input?: Record<string, unknown>;
  tool_output?: string;
  status: 'start' | 'end';
}

// 推荐问题数据接口
export interface IRecommendations {
  type: 'recommendations';
  questions: string[];
}

// 流式聊天接口
export const streamChat = (
  params: IChatRequest,
  callbacks: {
    onMessage: (content: string, isFinal: boolean) => void;
    onPreResponse: (content: string, stage: string) => void;
    onToolCall: (toolCall: IToolCall) => void;
    onRecommendations: (recommendations: string[]) => void;
    onEnd: () => void;
    onError: (error: Error) => void;
    onClose?: () => void; // 添加可选的 onClose 回调
  },
  signal?: AbortSignal,
) => {
  // 使用相对路径，webpack代理会转发到正确的服务器
  const url = '/humanrelation/chat';

  return fetchEventSource(url, {
    method: 'POST',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
      'x-requested-with': 'XMLHttpRequest',
      'X-Accel-Buffering': 'no',
      'X-Unbuffered': 'true',
      'access-token': getAccessToken() || '',
    },
    body: JSON.stringify(params),
    signal,
    onmessage(event) {
      try {
        const data = JSON.parse(event.data);
        console.log('🔍 [streamChat] 解析的数据:', data);

        // 处理聊天消息：type 为 'chat'
        if (data.type === 'chat' && data.content) {
          console.log('💬 [streamChat] 处理聊天消息:', data.content, data.is_final);
          callbacks.onMessage(String(data.content), Boolean(data.is_final));
        } else if (data.type === 'pre_response') {
          console.log('🔍 [streamChat] 收到预响应内容:', data.content, data.stage);
          callbacks.onPreResponse(String(data.content), String(data.stage));
        }
        // 处理工具调用：有 tool_name 和 status 字段
        else if (data.tool_name && data.status) {
          console.log('🔧 [streamChat] 处理工具调用:', data);
          const toolCall: IToolCall = {
            tool_name: data.tool_name,
            tool_input: data.tool_input,
            tool_output: data.tool_output,
            status: data.status,
          };
          callbacks.onToolCall(toolCall);
        }
        // 处理推荐问题：type 为 'recommendations'
        else if (data.type === 'recommendations' && data.questions && Array.isArray(data.questions)) {
          console.log('💡 [streamChat] 处理推荐问题:', data.questions);
          callbacks.onRecommendations(data.questions as string[]);
        }
        // 处理结束信号
        else if (data.type === 'end') {
          console.log('🏁 [streamChat] 收到结束信号');
          callbacks.onEnd();
        }
      } catch (error) {
        console.error('解析SSE消息失败:', error);
      }
    },
    onerror(error) {
      console.error('SSE连接错误:', error);

      // 如果是 AbortError，说明是用户主动取消，不需要当作错误处理
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('SSE连接被用户主动取消');
        return; // 不抛出错误，也不调用 onError 回调
      }

      callbacks.onError(error instanceof Error ? error : new Error(String(error)));
      throw error; // 重新抛出错误以停止重连
    },
    onclose() {
      console.log('SSE连接已关闭');
      // 调用可选的 onClose 回调
      if (callbacks.onClose) {
        callbacks.onClose();
      }
    },
  });
};

// 生成新的会话id
export const createConversation = async (params: ICreateConversationRequest): Promise<ICreateConversationResponse> => {
  const url = '/humanrelation/create_conversation';

  try {
    const response = await fetch(url, {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
        'x-requested-with': 'XMLHttpRequest',
        'access-token': getAccessToken() || '',
      },
    });
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    // 解析响应数据
    const data = await response.json();
    return data;
  } catch (error: unknown) {
    // 如果接口调用失败，返回模拟响应
    const mockConversationId = `${params.user_id}_${new Date().toISOString().slice(0, 19).replace(/[T:]/g, '-')}`;
    return {
      success: true,
      conversation_id: mockConversationId,
    };
  }
};
