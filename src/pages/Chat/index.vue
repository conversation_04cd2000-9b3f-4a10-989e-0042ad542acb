<template>
  <div class="v-chat-container">
    <!-- 星空粒子背景 -->
    <StarfieldBackground />

    <!-- AI助手选择界面 -->
    <div v-if="currentStep === 'selection'" class="ai-selection-container">
      <div class="ai-selection-content" :style="{ background: currentAssistantBg }">
        <!-- 顶部选中的AI助手头像 -->
        <div class="selected-avatar-container">
          <img
            :src="assistants[selectedAssistantIndex].avatar"
            :alt="assistants[selectedAssistantIndex].name"
            class="selected-avatar"
          />
        </div>

        <!-- 欢迎文字 -->
        <div class="welcome-text">
          <div class="welcome-line">欢迎遇见老董</div>
          <div class="welcome-line">您的专属AI管家</div>
          <div class="welcome-line">{{ assistants[selectedAssistantIndex].description }}</div>
        </div>

        <!-- 底部Swiper选择器 -->
        <div class="avatar-swiper-container">
          <swiper
            :slides-per-view="6"
            :space-between="20"
            :centered-slides="true"
            :loop="false"
            :grab-cursor="true"
            :initial-slide="selectedAssistantIndex"
            class="avatar-swiper"
            @swiper="onSwiperInit"
            @slide-change="handleSlideChange"
          >
            <swiper-slide
              v-for="(assistant, index) in assistants"
              :key="assistant.id"
              class="avatar-slide"
              :class="{ active: index === selectedAssistantIndex }"
            >
              <img
                :src="assistant.avatar"
                :alt="assistant.name"
                class="swiper-avatar"
                @click="selectAssistant(index)"
              />
            </swiper-slide>
          </swiper>
        </div>

        <!-- 底部按钮 -->
        <div class="selection-button-container">
          <button class="selection-btn" @click="goToIntroduction">选一个形象，进一步了解</button>
        </div>
      </div>
    </div>

    <!-- AI助手介绍界面 -->
    <div v-else-if="currentStep === 'introduction'" class="ai-introduction-container">
      <div class="ai-introduction-content" :style="{ background: currentAssistantBg }">
        <div class="selected-avatar-header">
          <h2 class="welcome-title">关于老董</h2>
        </div>

        <!-- Tab切换 -->
        <div class="intro-tab-switch" @click="toggleIntroTab">
          <div class="tab-slider" :class="{ 'slide-right': activeIntroTab === 'text' }"></div>
          <div class="tab-option" :class="{ active: activeIntroTab === 'video' }">视频介绍</div>
          <div class="tab-option" :class="{ active: activeIntroTab === 'text' }">文字介绍</div>
        </div>

        <!-- 介绍内容 -->
        <div class="intro-content">
          <div v-if="activeIntroTab === 'video'" class="video-intro">
            <div class="video-container">
              <video
                class="intro-video"
                controls
                preload="metadata"
                :poster="assistants[selectedAssistantIndex].avatar"
              >
                <source
                  src="https://s3plus.meituan.net/mcopilot-pub/70f2874b2cd675e9d6d2b76eb80cd275.mp4"
                  type="video/mp4"
                />
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
          <div v-else class="text-intro">
            <p>{{ assistants[selectedAssistantIndex].detailedDescription }}</p>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="introduction-button-container">
          <button class="introduction-btn" @click="startChat">培养老董，开始聊天/安排任务</button>
        </div>
        <!-- 底部欢迎语 -->
        <div class="introduction-bottom-text">
          <p>让我们开始这段美好的旅程~</p>
        </div>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div v-else-if="currentStep === 'chat'" class="welcome-container">
      <div class="welcome-content" :style="{ background: currentAssistantBg }">
        <div class="welcome-header">
          <div class="welcome-title-row">
            <div class="welcome-icon">
              <img
                :src="assistants[selectedAssistantIndex].avatar"
                :alt="assistants[selectedAssistantIndex].name"
                class="assistant-avatar"
              />
            </div>
            <div class="cyber-title">我是老董</div>
            <div class="intimacy-display">
              <span class="intimacy-label">当前懂量：</span>
              <span class="intimacy-value">{{ intimacyData.intimacy_score }}</span>
            </div>
          </div>
        </div>

        <!-- 小美功能卡片区域 -->
        <div class="future-features-section">
          <div class="cyber-title section-title">我已经有这些能力</div>
          <div class="xiaomei-cards-container">
            <XiaomeiItem
              v-for="item in xiaomeiItems"
              :key="item.id"
              :title="item.title"
              :icon="item.icon"
              @click="item.handler"
            />
          </div>
        </div>

        <!-- 未来可以做的事 -->
        <div class="future-features-section">
          <div class="cyber-title section-title">未来可以做的事</div>
          <div class="future-cards-container">
            <FutureItem
              v-for="item in futureItems"
              :key="item.id"
              :title="item.title"
              :icon="item.icon"
              @show-lock-dialog="handleShowLockDialog"
            />
          </div>
        </div>

        <!-- 亲密度显示模块 -->
        <div class="intimacy-container">
          <div class="intimacy-content">
            <!-- 左侧曲线图标 -->
            <div class="intimacy-curve">
              <img :src="polylineIcon" alt="懂量曲线" class="polyline-icon" />
            </div>
            <div class="intimacy-text-container">
              <div class="intimacy-title">关于"懂量"</div>
              <div class="intimacy-description">
                "懂量"代表了我对您的了解程度。与我交流越多，懂量提升越快，我能为您解锁的功能也越多！
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 亲密度说明弹窗 -->
    <div v-if="showIntimacyHelp" class="intimacy-popup-overlay" @click="showIntimacyHelp = false">
      <div class="intimacy-popup-content" :style="{ background: currentAssistantBg }" @click.stop>
        <!-- 关闭按钮 - 移到最右上角 -->
        <button class="intimacy-close-btn" @click="showIntimacyHelp = false">
          <span class="close-icon">×</span>
        </button>
        <!-- 顶部选中的AI助手头像 -->
        <div class="intimacy-popup-avatar-container">
          <img
            :src="assistants[selectedAssistantIndex].avatar"
            :alt="assistants[selectedAssistantIndex].name"
            class="intimacy-popup-avatar"
          />
        </div>
        <div class="intimacy-popup-header">
          <h3 class="intimacy-popup-title">懂量说明</h3>
        </div>
        <div class="intimacy-popup-body">
          <p class="intimacy-description">
            "懂量"代表了我对您的了解程度。与我交流越多，懂量提升越快，我能为您解锁的功能也越多！
          </p>
        </div>
      </div>
    </div>

    <!-- 功能锁定提示弹窗 -->
    <div v-if="showLockDialog" class="dialog-overlay">
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-title">功能锁定</div>
        </div>
        <div class="dialog-content">
          <div class="lock-message">即将上线，敬请期待</div>
        </div>
        <div class="dialog-footer">
          <button class="confirm-btn" @click="closeLockDialog">我知道了</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { showFailToast } from 'vant';
import { Swiper, SwiperSlide } from 'swiper/vue';
import type { Swiper as SwiperType } from 'swiper';
import 'swiper/css';
import StarfieldBackground from '@/components/StarfieldBackground.vue';
import XiaomeiItem from '@/components/Index/XiaomeiItem.vue';
import FutureItem from '@/components/Index/FutureItem.vue';
import { getUserInfo } from '@/apis/common';
import { getPersonWeather, getIntimacy } from '@/apis/memory';
// 导入头像图片
import avatar1 from '@/assets/icon/laodong1.jpg';
import avatar2 from '@/assets/icon/laodong2.jpg';
import avatar3 from '@/assets/icon/laodong3.png';
import avatar4 from '@/assets/icon/laodong4.png';
import avatar5 from '@/assets/icon/laodong5.png';
import avatar6 from '@/assets/icon/laodong6.jpg';
import polylineIcon from '@/assets/icon/polyline.png';

const router = useRouter();

// 用户信息
const currentUserId = ref('');

// 亲密度弹窗控制
const showIntimacyHelp = ref(false);

// 功能锁定弹窗控制
const showLockDialog = ref(false);

// 懂量数据
const intimacyData = ref({
  intimacy_score: 0,
  level: '',
  next_level: '',
  progress_to_next: 0,
  points_needed: 0,
});

// 懂量加载状态
const intimacyLoading = ref(false);

// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';
const AI_STEP_STORAGE_KEY = 'currentStep';

// 初始化函数：同步确定初始状态，避免闪烁
const getInitialStep = (): 'selection' | 'introduction' | 'chat' => {
  // 检查是否从home按钮跳转过来
  const fromHome = sessionStorage.getItem('fromHomeButton');
  if (fromHome === 'true') {
    return 'chat';
  }

  // 检查用户是否已经选择过AI助手
  const savedAssistant = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const savedStep = localStorage.getItem(AI_STEP_STORAGE_KEY);

  if (savedAssistant !== null) {
    // 如果用户已经选择过AI助手，检查保存的步骤状态
    if (savedStep === 'chat') {
      // 如果用户已经到达过聊天界面，直接进入聊天界面
      console.log('✅ [index.vue] 用户已完成选择流程，直接进入聊天界面');
      return 'chat';
    }
    if (savedStep === 'introduction') {
      // 如果用户在介绍界面，恢复到介绍界面
      console.log('🔄 [index.vue] 恢复到AI助手介绍界面');
      return 'introduction';
    }
    // 如果没有步骤记录但有助手选择，可能是第一次选择，进入介绍界面
    console.log('🔄 [index.vue] 用户已选择助手但未完成流程，进入介绍界面');
    return 'introduction';
  }

  // 如果用户从未选择过AI助手，进入选择界面
  console.log('⚠️ [index.vue] 用户未选择过AI助手，进入选择界面');
  return 'selection';
};

const getInitialAssistantIndex = (): number => {
  // 总是尝试从localStorage读取助手选择
  const saved = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  console.log('🔍 [index.vue] getInitialAssistantIndex - localStorage值:', saved);
  if (saved !== null) {
    const index = parseInt(saved, 10);
    console.log('🔍 [index.vue] getInitialAssistantIndex - 解析的索引:', index);
    // 这里先用一个合理的范围检查，后面会在组件挂载时进一步验证
    if (index >= 0 && index < 7) {
      console.log('✅ [index.vue] 从localStorage恢复助手选择:', index);
      return index;
    }
  }

  // 如果没有保存的选择，默认为第一个助理
  console.log('⚠️ [index.vue] 使用默认助手选择: 0');
  return 3;
};

// 当前步骤：selection(选择) -> introduction(介绍) -> chat(聊天)
const currentStep = ref<'selection' | 'introduction' | 'chat'>(getInitialStep());

// 当前选中的AI助手索引
const selectedAssistantIndex = ref(getInitialAssistantIndex());

// 介绍页面的Tab状态
const activeIntroTab = ref<'video' | 'text'>('video');

// Swiper实例引用
const swiperInstance = ref<SwiperType>();

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`,
    bgColor: 'rgba(0, 0, 0, 0.2)', // 黑色，降低透明度
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`,
    bgColor: 'rgba(147, 197, 253, 0.15)', // 浅蓝色，降低透明度
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`,
    bgColor: 'rgba(30, 58, 138, 0.2)', // 深蓝色，降低透明度
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`,
    bgColor: 'rgba(191, 219, 254, 0.1)', // 极浅蓝色，降低透明度
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`,
    bgColor: 'rgba(0, 0, 0, 0.2)', // 黑色，降低透明度 (重复laodong1)
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
    description: '懂你+懂干活=更好的助手',
    detailedDescription: `这一波AI火热2年多了，
可是还没有一个靠谱的个人助手。
为什么呢？
因为大多数AI助手没有关注用户个人的情况。

这些AI助手，
就类似于博物馆的讲解员。
他们，上知天文、下知地理，博学多才。
可惜，不知道你喜欢吃什么、
不知道去过哪里、
不知道你对什么感兴趣、
也不知道你工作中的烦恼……

我是老董，你的AI管家。
我有如下特点：
1、首先是了解你的情况，用小本本把你的情况、你亲友的情况都记下来，懂你才能服务好你；
2、我可以召唤其他AI助手，例如查天气的、订外卖的、订酒店的….. 一起为你服务。
我不是简单召唤他们，不是发送"明升今晚想点外卖火锅"这样简单的需求。
而是发送很多背景资料给他们，例如
"明升，xx岁，喜欢吃xxxx、不喜欢吃xxxx，
今晚和他一起吃饭的是他12岁的女儿和4岁的儿子。
女儿喜欢吃牛肉片、午餐肉、不喜欢吃麻酱、不喜欢吃内脏，；
儿子喜欢吃牛肉片、午餐肉还有烧饼、不需要蘸料，
过去一年，周日晚上在家点外卖的订单有30次，其中6次是火锅，这6次订单的情况是xxxx；
这顿饭在100元～200元之间都可以，希望19点左右送到；"
3、我接受了职业培训，知道有些事情不能问、有些事情不能说，有些事情不能记，以及不泄漏用户隐私。
4、我记录的信息，后续你可以修改、可以删除也可以下载备份。
5、类似养成游戏，你刚刚见到我的时候，我是小董，我们聊多了我就可以成长，变成大董、老董…..

现在，我们开始聊聊吧～

【重要提示】
用户输入的内容，尤其是语音模式的，可能存在错别字、同音字、近似词语以及口头禅、脏话，以及标点符号错误。老董会智能识别并纠正这些问题，确保理解用户的真实意图。`,
    bgColor: 'rgba(147, 197, 253, 0.15)', // 浅蓝色，降低透明度 (重复laodong2)
  },
]);

// 当前助手背景色
const currentAssistantBg = computed(() => {
  return assistants.value[selectedAssistantIndex.value].bgColor;
});

// AI助理头像选择的持久化存储函数
const saveAssistantSelection = (index: number) => {
  localStorage.setItem(AI_ASSISTANT_STORAGE_KEY, index.toString());
  console.log('💾 [index.vue] 保存AI助理选择:', index);
  // 立即验证保存是否成功
  const saved = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  console.log('🔍 [index.vue] 保存后立即读取localStorage:', saved);
};

const saveCurrentStep = (step: 'selection' | 'introduction' | 'chat') => {
  localStorage.setItem(AI_STEP_STORAGE_KEY, step);
  console.log('💾 [index.vue] 保存当前步骤:', step);
};

// AI助手选择相关方法
const selectAssistant = (index: number) => {
  console.log('🎯 [index.vue] selectAssistant 被调用，索引:', index);
  selectedAssistantIndex.value = index;
  // 立即保存选择到localStorage
  saveAssistantSelection(index);
  console.log('💾 [index.vue] selectAssistant 保存后，localStorage值:', localStorage.getItem(AI_ASSISTANT_STORAGE_KEY));
  // 让选中的头像滑动到中央
  if (swiperInstance.value) {
    swiperInstance.value.slideTo(index);
  }
};

const handleSlideChange = (swiper: { activeIndex: number }) => {
  console.log('🔄 [index.vue] handleSlideChange 被调用，索引:', swiper.activeIndex);
  selectedAssistantIndex.value = swiper.activeIndex;
  // 立即保存选择到localStorage
  saveAssistantSelection(swiper.activeIndex);
  console.log(
    '💾 [index.vue] handleSlideChange 保存后，localStorage值:',
    localStorage.getItem(AI_ASSISTANT_STORAGE_KEY),
  );
};

// Swiper初始化回调
const onSwiperInit = (swiper: SwiperType) => {
  swiperInstance.value = swiper;
};

const goToIntroduction = () => {
  currentStep.value = 'introduction';
  saveCurrentStep('introduction');
};

// 介绍页面Tab切换
const toggleIntroTab = () => {
  activeIntroTab.value = activeIntroTab.value === 'video' ? 'text' : 'video';
};

const startChat = () => {
  currentStep.value = 'chat';
  saveCurrentStep('chat');
};

// 直接跳转到聊天界面的方法（供home按钮使用）
const goToChat = () => {
  currentStep.value = 'chat';
  saveCurrentStep('chat');
  console.log('🏠 [index.vue] 直接跳转到聊天界面');
};

// 小美功能卡片数据
const xiaomeiItems = ref([
  {
    id: 1,
    title: '了解您的情况',
    icon: '📃',
    handler: handleRecordMasterSituation,
  },
  {
    id: 2,
    title: '了解您的亲友',
    icon: '👥',
    handler: handleMyRelations,
  },
  {
    id: 3,
    title: '一起讨论问题',
    icon: '💬',
    handler: handleChatWithMe,
  },
  {
    id: 4,
    title: '天气解读/预警',
    icon: '🌤️',
    handler: handleWeatherForecast,
  },
]);

// 未来功能卡片数据
const futureItems = ref([
  {
    id: 1,
    title: '旅游规划',
    icon: '✈️',
  },
  {
    id: 2,
    title: '打车',
    icon: '🚗',
  },
  {
    id: 3,
    title: '点外卖',
    icon: '🍔',
  },
  {
    id: 4,
    title: '防诈骗',
    icon: '🛡️',
  },
]);

// 处理我的亲友点击
async function handleMyRelations() {
  console.log('🔄 [index.vue] 我的亲友卡片点击，跳转到关系图谱页面');

  // 记录来源页面为index
  sessionStorage.setItem('relationGraphSource', 'index');

  await router.push({
    name: 'relationship-graph',
  });
}

// 处理天气预报点击
async function handleWeatherForecast() {
  console.log('🔄 [index.vue] 天气预报卡片点击，跳转到聊天页面');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
    query: { cardType: 'weather' },
  });
}

// 处理提醒事件点击
async function handleReminders() {
  console.log('🔄 [index.vue] 提醒事件卡片点击，跳转到聊天页面');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
    query: { initialMessage: '我有哪些提醒事件？' },
  });
}

// 处理和我聊天点击
async function handleChatWithMe() {
  console.log('🔄 [index.vue] 和我聊天卡片点击，直接跳转到聊天页面');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
  });
}

// 处理记录您的情况点击
async function handleRecordMasterSituation() {
  console.log('🔄 [index.vue] 记录您的情况卡片点击，跳转到聊天页面');

  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
    query: { cardType: 'record' },
  });
}

// 处理显示功能锁定弹窗
function handleShowLockDialog() {
  console.log('🔄 [index.vue] 显示功能锁定弹窗');
  showLockDialog.value = true;
}

// 关闭功能锁定弹窗
function closeLockDialog() {
  console.log('🔄 [index.vue] 关闭功能锁定弹窗');
  showLockDialog.value = false;
}

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [index.vue] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [index.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      console.log('✅ [index.vue] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [index.vue] 用户信息格式异常');
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [index.vue] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 获取懂量数据
const loadIntimacyData = async () => {
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    console.warn('⚠️ [index.vue] 用户ID无效，跳过懂量数据获取');
    return;
  }

  try {
    console.log('🔄 [index.vue] 开始获取懂量数据...');
    intimacyLoading.value = true;

    const response = await getIntimacy({
      user_id: currentUserId.value,
    });

    console.log('📡 [index.vue] 懂量数据响应:', response);

    if (response && response.result === 'success') {
      intimacyData.value = {
        intimacy_score: response.intimacy_score,
        level: response.level,
        next_level: response.next_level,
        progress_to_next: response.progress_to_next,
        points_needed: response.points_needed,
      };
      console.log('✅ [index.vue] 懂量数据加载成功:', intimacyData.value);
    } else {
      console.warn('⚠️ [index.vue] 懂量数据响应格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [index.vue] 获取懂量数据失败:', error);
  } finally {
    intimacyLoading.value = false;
  }
};

onMounted(async () => {
  console.log('🔄 [index.vue] onMounted 开始');
  console.log('🔍 [index.vue] onMounted - 当前助手索引:', selectedAssistantIndex.value);
  console.log('🔍 [index.vue] onMounted - 助手数组长度:', assistants.value.length);
  console.log('🔍 [index.vue] onMounted - localStorage值:', localStorage.getItem(AI_ASSISTANT_STORAGE_KEY));

  // 加载用户信息
  await loadUserInfo();

  // 加载懂量数据
  await loadIntimacyData();

  // 验证助手索引是否在有效范围内
  if (selectedAssistantIndex.value >= assistants.value.length) {
    console.log(
      '⚠️ [index.vue] 助手索引超出范围，当前索引:',
      selectedAssistantIndex.value,
      '数组长度:',
      assistants.value.length,
    );
    selectedAssistantIndex.value = 0;
    saveAssistantSelection(0);
    console.log('🔧 [index.vue] 助手索引超出范围，重置为0');
  } else {
    console.log('✅ [index.vue] 助手索引在有效范围内:', selectedAssistantIndex.value);
  }

  // 检查是否从home按钮跳转过来
  const fromHome = sessionStorage.getItem('fromHomeButton');
  if (fromHome === 'true') {
    sessionStorage.removeItem('fromHomeButton');
    console.log('🏠 [index.vue] 从home按钮跳转，保持之前的AI助理选择');
  } else {
    // 页面刷新时保留AI助理选择和步骤状态，实现持久化
    console.log('🔄 [index.vue] 页面刷新，保留AI助理选择和步骤状态');
  }

  // 确保助手选择已保存（如果用户之前选择过）
  if (selectedAssistantIndex.value !== 0) {
    saveAssistantSelection(selectedAssistantIndex.value);
    console.log('� [index.vue] 确保助手选择已保存:', selectedAssistantIndex.value);
  }

  console.log('✅ [index.vue] onMounted 完成，最终助手索引:', selectedAssistantIndex.value);
});

// 暴露方法供外部使用
defineExpose({
  goToChat,
  selectAssistant,
  selectedAssistantIndex,
  currentStep,
});
</script>

<style lang="scss" scoped>
.v-chat-container {
  background: url('@/assets/img/galaxy5.jpg') center center / cover no-repeat;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

// 统一的容器样式
.ai-selection-container,
.ai-introduction-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  height: 900px; // 固定高度
  z-index: 10;
}

.welcome-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  height: 92%; // 固定高度
  z-index: 10;
}

// 统一的内容样式
.ai-selection-content,
.ai-introduction-content,
.welcome-content {
  border-radius: 16px;
  padding: 40px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 0 12px rgba(0, 255, 255, 0.4),
    // 增强青色光晕
    0 8px 32px rgba(0, 0, 0, 0.7),
    // 增强深色外阴影，更远更深
    0 4px 16px rgba(0, 0, 0, 0.4),
    // 添加中层阴影
    inset 0 2px 0 rgba(255, 255, 255, 0.15),
    // 增强内部高光
    inset 0 -1px 0 rgba(0, 0, 0, 0.2); // 添加底部内阴影增加深度
  transition: all 0.3s ease;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: center;
  height: 100%; // 填满容器
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

// welcome界面使用动态背景，不再设置固定背景色

.welcome-header {
  text-align: center;
}

.welcome-title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;

  .intimacy-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-left: var(--spacing-md);

    .intimacy-label {
      font-size: var(--font-size-xl);
      color: var(--text-secondary);
      font-weight: 500;
    }

    .intimacy-value {
      font-size: var(--font-size-xl);
      font-weight: 700;
      background: linear-gradient(135deg, var(--primary-color) 0%, #00ffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(1.2);
      }
    }
  }
}

// 统一的区域标题样式
.section-title {
  margin-bottom: 20px !important;
}

// 选中的AI助手头像容器
.selected-avatar-container {
  margin-bottom: 20px; // 增加底部间距，让头像位置向下一点
  margin-top: 20px; // 增加顶部间距，让头像位置向下一点

  .selected-avatar {
    width: 180px; // 放大头像 (150px -> 170px)
    height: 180px; // 放大头像 (150px -> 170px)
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
  }
}

// 小美功能卡片容器
.xiaomei-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 28px;
  margin-top: 0px;
  margin-bottom: 20px;
  width: 100%;
}

// 未来功能区域
.future-features-section {
  margin-bottom: 20px;
  width: 100%;
}

.future-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  width: 100%;
}

// 选中头像的标题区域
.selected-avatar-header {
  text-align: center;

  .welcome-title {
    font-size: 38px;
    font-weight: 600;
    color: #00bcd4;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0;
    margin-bottom: 50px;
    margin-top: 50px;
  }
}

// 介绍页面底部文字
.introduction-bottom-text {
  text-align: center;
  margin-top: 5px;

  p {
    font-size: 24px;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0;
    opacity: 0.9;
  }
}

// 欢迎文字
.welcome-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .welcome-line {
    font-size: 36px; // 增加4px (24px -> 28px)
    font-weight: 600;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

    &:last-child {
      margin-bottom: 0;
      font-size: 26px; // 增加4px (20px -> 24px)
      opacity: 0.9;
      margin-top: 20px;
    }
  }
}

// Swiper容器
.avatar-swiper-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar-swiper {
    width: 100%;
    max-width: 600px; // 增加宽度以容纳6个头像 (500px -> 600px)
    height: 120px;
    padding: 20px 0;
  }

  .avatar-slide {
    width: 90px !important; // 放大slide容器 (80px -> 90px)
    height: 90px; // 放大slide容器 (80px -> 90px)
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;

    &.active {
      transform: scale(1.3);

      .swiper-avatar {
        border-color: rgba(0, 255, 255, 0.8);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
      }
    }

    .swiper-avatar {
      width: 70px; // 放大头像 (60px -> 70px)
      height: 70px; // 放大头像 (60px -> 70px)
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(255, 255, 255, 0.8);
        transform: scale(1.1);
      }
    }
  }
}

// 选择按钮
.selection-button-container {
  margin-top: auto;

  .selection-btn {
    background: rgba(0, 188, 212, 0.3); // 改为低透明度 (0.8 -> 0.3)
    border: 2px solid rgba(0, 188, 212, 0.6); // 边框也调整透明度
    border-radius: 32px; // 增大圆角 (28px -> 32px)
    margin-bottom: 30px;
    padding: 20px 40px; // 放大按钮 (16px 32px -> 20px 40px)
    color: white;
    font-size: 26px; // 再增加2px (22px -> 24px)
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-height: 60px; // 增加最小高度

    &:hover {
      background: rgba(0, 188, 212, 0.5); // hover状态也调整为低透明度
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.4);
    }
  }
}

.welcome-icon {
  flex-shrink: 0; // 防止头像被压缩
  display: flex;
  align-items: center;
  justify-content: center;

  .assistant-avatar {
    width: 80px; // 调整头像大小以适应横向布局
    height: 80px; // 调整头像大小以适应横向布局
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--accent-color-medium);
    box-shadow: var(--shadow-accent);
  }
}

// 介绍页面Tab切换样式
.intro-tab-switch {
  position: relative;
  display: flex;
  background: rgba(0, 188, 212, 0.1);
  border: 2px solid #00bcd4;
  border-radius: 36px; // 再增大圆角 (32px -> 36px)
  padding: 10px; // 再增大内边距 (8px -> 10px)
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 100px;
  overflow: hidden;
  width: 500px; // 适度增大宽度以适应更大字体
  min-width: 420px;
  height: 72px; // 再增大高度 (64px -> 72px)
  margin: 0 auto 30px auto;

  &:hover {
    background: rgba(0, 188, 212, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }

  .tab-slider {
    position: absolute;
    top: 10px; // 对应padding的增加 (8px -> 10px)
    left: 10px;
    width: calc(50% - 10px); // 对应padding的增加 (8px -> 10px)
    height: calc(100% - 20px); // 对应padding的增加 (16px -> 20px)
    background: #00bcd4;
    border-radius: 30px; // 增大圆角 (26px -> 30px)
    transition: transform 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 188, 212, 0.4);

    &.slide-right {
      transform: translateX(100%);
    }
  }

  .tab-option {
    flex: 1;
    padding: 16px 30px; // 再增大内边距 (14px 26px -> 16px 30px)
    text-align: center;
    font-size: 30px; // 再增加2px (28px -> 30px)
    font-weight: 500;
    color: #00bcd4;
    transition: color 0.3s ease;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      color: white;
    }
  }
}

// 介绍内容样式
.intro-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 380px; // 设置最小高度，防止挤占tab-switch空间
  max-height: 380px; // 设置最大高度，保持一致性

  .video-intro {
    width: 100%;
    height: 100%; // 填满容器高度
    display: flex;
    align-items: center;
    justify-content: center;

    .video-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20px;
      overflow: hidden;
      background: rgba(0, 0, 0, 0.3);
      border: 2px solid rgba(0, 255, 255, 0.3);
      box-shadow:
        0 0 15px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);

      .intro-video {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 18px; // 稍小于容器圆角
        background: rgba(0, 0, 0, 0.8);
      }
    }
  }

  .text-intro {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px; // 增大圆角 (16px -> 20px)
    padding: 35px; // 增大内边距 (30px -> 35px)
    backdrop-filter: blur(10px);
    width: 100%;
    height: 100%; // 设置固定高度，与video-placeholder保持一致
    overflow-y: auto;
    // 添加有阴影的边框
    border: 2px solid rgba(0, 255, 255, 0.3);
    box-shadow:
      0 0 15px rgba(0, 255, 255, 0.2),
      inset 0 0 20px rgba(0, 255, 255, 0.1);
    box-sizing: border-box; // 确保padding不会影响总高度

    p {
      font-size: 20px; // 再增加2px (18px -> 20px)
      line-height: 1.8; // 增加行高便于阅读
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      text-align: left; // 左对齐便于阅读长文本
      white-space: pre-line; // 保持换行格式
    }
  }
}

// 介绍按钮
.introduction-button-container {
  margin-top: auto;

  .introduction-btn {
    background: rgba(0, 188, 212, 0.3); // 改为低透明度，与选择按钮保持一致
    border: 2px solid rgba(0, 188, 212, 0.6); // 边框也调整透明度
    border-radius: 32px; // 增大圆角 (28px -> 32px)
    margin-bottom: 5px; // 添加底部间距，与选择按钮保持一致
    padding: 20px 40px; // 放大按钮 (16px 32px -> 20px 40px)
    color: white;
    font-size: 26px; // 调整为与选择按钮一致的字体大小
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-height: 60px; // 增加最小高度

    &:hover {
      background: rgba(0, 188, 212, 0.5); // hover状态也调整为低透明度
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.4);
    }
  }
}

// 亲密度模块样式
.intimacy-container {
  margin-top: 20px;
  padding: 0 10px;
}

.intimacy-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 20px 24px;
  background: rgba(113, 229, 247, 0.15);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
}

.intimacy-curve {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .polyline-icon {
    width: 50px;
    height: 40px;
    object-fit: contain;
    margin-right: 10px;
  }
}

.intimacy-text-container {
  flex: 1;
  text-align: left;
}

.intimacy-title {
  color: #00ffff;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.intimacy-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 20px;
  font-weight: 400;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

// 亲密度弹窗样式 - 与index页面统一
.intimacy-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.intimacy-popup-content {
  border-radius: 16px;
  padding: 40px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 0 12px rgba(0, 255, 255, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.7),
    0 4px 16px rgba(0, 0, 0, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  max-width: 90vw;
  max-height: 80vh;
  width: 500px;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

// AI助手头像容器 - 与index页面统一
.intimacy-popup-avatar-container {
  margin-bottom: 20px;
  margin-top: 20px;

  .intimacy-popup-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
  }
}

.intimacy-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.intimacy-popup-title {
  color: #00bcd4;
  font-size: 28px; // 增加4px (24px -> 28px)
  font-weight: 600;
  margin: 0;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.intimacy-close-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  .close-icon {
    color: white;
    font-size: 24px; // 增加4px (20px -> 24px)
    font-weight: bold;
    line-height: 1;
  }
}

.intimacy-popup-body {
  .intimacy-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 22px; // 增加4px (18px -> 22px)
    line-height: 1.6;
    margin: 0;
    text-align: center;
  }
}

// 功能锁定弹窗样式 - 与relationGraph页面的deleteEventDialog保持一致
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  width: 90%;
  max-width: 500px;
  min-height: 300px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  height: 80px;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .lock-message {
    color: rgba(255, 255, 255, 0.9);
    font-size: 28px;
    font-weight: 500;
    line-height: 1.6;
    letter-spacing: 0.5px;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 28px;
    font-weight: 600;
    border: 2px solid;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 1px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>
