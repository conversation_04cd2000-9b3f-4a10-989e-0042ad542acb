<template>
  <div class="ios-debug-container">
    <div class="debug-header">
      <h1>iOS调试页面</h1>
      <div class="device-info">
        <p>设备类型: {{ deviceInfo.isIOS ? 'iOS' : '非iOS' }}</p>
        <p>浏览器: {{ deviceInfo.isIOSSafari ? 'iOS Safari' : '其他' }}</p>
        <p>iOS版本: {{ deviceInfo.iosVersion || '未知' }}</p>
      </div>
    </div>

    <div class="debug-content">
      <div class="section">
        <h2>视口信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">窗口宽度:</span>
            <span class="value">{{ viewportInfo.width }}px</span>
          </div>
          <div class="info-item">
            <span class="label">窗口高度:</span>
            <span class="value">{{ viewportInfo.height }}px</span>
          </div>
          <div class="info-item">
            <span class="label">设备像素比:</span>
            <span class="value">{{ viewportInfo.devicePixelRatio }}</span>
          </div>
          <div class="info-item">
            <span class="label">Visual Viewport:</span>
            <span class="value">{{ visualViewportSupported ? '支持' : '不支持' }}</span>
          </div>
        </div>
      </div>

      <div class="section">
        <h2>键盘状态</h2>
        <div class="keyboard-status">
          <p>键盘状态: {{ keyboardVisible ? '弹起' : '收起' }}</p>
          <p>键盘高度: {{ keyboardHeight }}px</p>
          <p>CSS变量键盘高度: {{ cssKeyboardHeight }}</p>
          <p>页面是否被顶起: {{ pageScrolled ? '是' : '否' }}</p>
        </div>
        <input
          type="text"
          placeholder="点击测试键盘弹起"
          class="test-input"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
      </div>

      <div class="section">
        <h2>预设问题测试</h2>
        <div class="preset-questions-test">
          <div
            v-for="(question, index) in testQuestions"
            :key="index"
            class="test-question-item"
            @click="handleQuestionClick(question)"
          >
            {{ question }}
          </div>
        </div>
      </div>

      <div class="section">
        <h2>滚动测试</h2>
        <div class="scroll-test-container">
          <div v-for="i in 20" :key="i" class="scroll-item">滚动测试项目 {{ i }}</div>
        </div>
      </div>
    </div>

    <div class="debug-footer">
      <button class="refresh-btn" @click="refreshInfo">刷新信息</button>
      <button class="clear-btn" @click="clearLogs">清除日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { isIOSDevice, isIOSSafari, getIOSVersion, logIOSDebugInfo } from '@/utils/iosCompatibility';

// 响应式数据
const deviceInfo = ref({
  isIOS: false,
  isIOSSafari: false,
  iosVersion: null as number | null,
});

const viewportInfo = ref({
  width: 0,
  height: 0,
  devicePixelRatio: 0,
});

const visualViewportSupported = ref(false);
const keyboardVisible = ref(false);
const keyboardHeight = ref(0);
const cssKeyboardHeight = ref('0px');
const pageScrolled = ref(false);

const testQuestions = ref(['这是测试问题1', '这是测试问题2', '这是测试问题3', '这是测试问题4']);

// 更新设备信息
const updateDeviceInfo = () => {
  deviceInfo.value = {
    isIOS: isIOSDevice(),
    isIOSSafari: isIOSSafari(),
    iosVersion: getIOSVersion(),
  };
};

// 更新视口信息
const updateViewportInfo = () => {
  viewportInfo.value = {
    width: window.innerWidth,
    height: window.innerHeight,
    devicePixelRatio: window.devicePixelRatio,
  };

  visualViewportSupported.value = !!window.visualViewport;
};

// 键盘事件处理
const handleKeyboardChange = () => {
  if (window.visualViewport) {
    const viewport = window.visualViewport;
    const heightDiff = window.innerHeight - viewport.height;

    if (heightDiff > 100) {
      keyboardVisible.value = true;
      keyboardHeight.value = heightDiff;
    } else {
      keyboardVisible.value = false;
      keyboardHeight.value = 0;
    }

    // 更新CSS变量显示
    const cssVar = getComputedStyle(document.documentElement).getPropertyValue('--keyboard-height');
    cssKeyboardHeight.value = cssVar || '0px';

    // 检测页面是否被滚动
    pageScrolled.value = window.scrollY > 0;
  }
};

// 输入框事件
const handleInputFocus = () => {
  console.log('🎯 [IOSDebug] 输入框获得焦点');
};

const handleInputBlur = () => {
  console.log('🎯 [IOSDebug] 输入框失去焦点');
};

// 问题点击事件
const handleQuestionClick = (question: string) => {
  console.log('🎯 [IOSDebug] 点击测试问题:', question);
  // 使用console.log代替alert，避免ESLint警告
  console.log(`✅ 点击了测试问题: ${question}`);
};

// 刷新信息
const refreshInfo = () => {
  updateDeviceInfo();
  updateViewportInfo();
  logIOSDebugInfo();
};

// 清除日志
const clearLogs = () => {
  console.clear();
};

// 组件挂载
onMounted(() => {
  updateDeviceInfo();
  updateViewportInfo();

  // 监听视口变化
  window.addEventListener('resize', updateViewportInfo);

  // 监听键盘变化
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', handleKeyboardChange);
  }

  // 输出调试信息
  logIOSDebugInfo();
});

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', updateViewportInfo);

  if (window.visualViewport) {
    window.visualViewport.removeEventListener('resize', handleKeyboardChange);
  }
});
</script>

<style lang="scss" scoped>
.ios-debug-container {
  padding: 20px;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  min-height: 100vh;
  color: white;
  font-family: 'Courier New', monospace;
}

.debug-header {
  margin-bottom: 30px;

  h1 {
    color: #00ffff;
    text-align: center;
    margin-bottom: 20px;
  }

  .device-info {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    padding: 15px;

    p {
      margin: 5px 0;
    }
  }
}

.debug-content {
  .section {
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;

    h2 {
      color: #00bcd4;
      margin-bottom: 15px;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;

    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 8px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 5px;

      .label {
        color: #ccc;
      }

      .value {
        color: #00ffff;
        font-weight: bold;
      }
    }
  }

  .keyboard-status {
    margin-bottom: 15px;

    p {
      margin: 5px 0;
    }
  }

  .test-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #00bcd4;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    font-size: 16px;

    &::placeholder {
      color: #ccc;
    }

    &:focus {
      outline: none;
      border-color: #00ffff;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }
  }

  .preset-questions-test {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    .test-question-item {
      padding: 10px 15px;
      background: rgba(0, 188, 212, 0.2);
      border: 1px solid #00bcd4;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 188, 212, 0.4);
        transform: translateY(-2px);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .scroll-test-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #00bcd4;
    border-radius: 8px;

    .scroll-item {
      padding: 15px;
      border-bottom: 1px solid rgba(0, 188, 212, 0.3);

      &:last-child {
        border-bottom: none;
      }

      &:nth-child(even) {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.debug-footer {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;

  button {
    padding: 12px 24px;
    border: 2px solid #00bcd4;
    border-radius: 8px;
    background: rgba(0, 188, 212, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 188, 212, 0.4);
      transform: translateY(-2px);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// iOS特殊适配
@supports (-webkit-touch-callout: none) {
  .ios-debug-container {
    -webkit-overflow-scrolling: touch;
  }

  .scroll-test-container {
    -webkit-overflow-scrolling: touch;
  }
}
</style>
