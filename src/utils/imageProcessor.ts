/**
 * 图片预处理工具函数
 * 用于处理用户头像，确保在关系图谱中正确显示
 */

/**
 * 检测是否为iOS设备
 */
const isIOSDevice = (): boolean => {
  const ua = navigator.userAgent;
  return (
    /iPad|iPhone|iPod/.test(ua) ||
    // 检测iPad Pro在桌面模式下的情况
    (/Macintosh/.test(ua) && navigator.maxTouchPoints > 1)
  );
};

/**
 * 检测是否为iOS Safari浏览器
 */
const isIOSSafari = (): boolean => {
  const ua = navigator.userAgent;
  return isIOSDevice() && /Safari/.test(ua) && !/Chrome|CriOS|FxiOS/.test(ua);
};

/**
 * iOS Safari Canvas兼容性处理
 * 解决iOS Safari在Canvas处理上的各种兼容性问题
 */
const createIOSCompatibleCanvas = (
  size: number,
): { canvas: HTMLCanvasElement; ctx: CanvasRenderingContext2D } | null => {
  try {
    const canvas = document.createElement('canvas');

    // iOS设备需要考虑设备像素比，但限制最大值避免内存问题
    const devicePixelRatio = isIOSDevice() ? Math.min(window.devicePixelRatio || 1, 2) : 1;
    const actualSize = size * devicePixelRatio;

    // 设置Canvas尺寸
    canvas.width = actualSize;
    canvas.height = actualSize;

    // 获取2D上下文，使用iOS优化配置
    const ctx = canvas.getContext('2d', {
      alpha: true,
      desynchronized: false,
      willReadFrequently: false,
    });

    if (!ctx) {
      console.error('无法获取Canvas 2D上下文');
      return null;
    }

    // 缩放上下文以匹配设备像素比
    if (devicePixelRatio !== 1) {
      ctx.scale(devicePixelRatio, devicePixelRatio);
    }

    // iOS Safari兼容性设置
    ctx.globalCompositeOperation = 'source-over';

    // iOS Safari优化：使用更好的图像渲染质量
    if (isIOSSafari()) {
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
    }

    return { canvas, ctx };
  } catch (error) {
    console.error('创建Canvas失败:', error);
    return null;
  }
};

/**
 * iOS Safari兼容的Canvas转Base64
 * 处理iOS Safari在toDataURL方法上的兼容性问题
 */
const canvasToDataURL = (canvas: HTMLCanvasElement, fallbackUrl: string): string => {
  try {
    // iOS Safari在某些情况下toDataURL可能失败，使用更保守的质量设置
    const quality = isIOSSafari() ? 0.8 : 0.9;
    const dataUrl = canvas.toDataURL('image/png', quality);

    // 验证生成的base64是否有效
    if (!dataUrl || dataUrl === 'data:,') {
      throw new Error('Canvas toDataURL 返回无效数据');
    }

    return dataUrl;
  } catch (canvasError) {
    console.warn('Canvas toDataURL 失败，尝试备用方案:', canvasError);

    // 备用方案：使用较低质量重试
    try {
      return canvas.toDataURL('image/jpeg', 0.7);
    } catch (fallbackError) {
      console.error('备用方案也失败，返回原始图片:', fallbackError);
      return fallbackUrl;
    }
  }
};

/**
 * 将任意形状的图片处理成带白色背景的圆形图片
 * @param imageUrl 原始图片URL
 * @returns Promise<string> 处理后的base64图片URL
 */
export const processAvatarImage = (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();

    // iOS Safari特殊处理：添加超时机制
    const loadTimeout = setTimeout(
      () => {
        console.warn('图片加载超时，使用原始图片:', imageUrl);
        resolve(imageUrl);
      },
      isIOSSafari() ? 10000 : 5000,
    ); // iOS Safari给更长的超时时间

    // iOS Safari需要特殊处理跨域问题
    if (isIOSSafari()) {
      // 对于iOS Safari，如果是同源图片或data URL，不设置crossOrigin
      if (imageUrl.startsWith('data:') || imageUrl.startsWith('blob:') || imageUrl.startsWith(window.location.origin)) {
        // 不设置crossOrigin，避免iOS Safari的CORS问题
      } else {
        img.crossOrigin = 'anonymous';
      }
    } else {
      img.crossOrigin = 'anonymous'; // 处理跨域问题
    }

    const handleImageLoad = () => {
      clearTimeout(loadTimeout);
      try {
        // 设置canvas尺寸为正方形，使用固定尺寸确保一致性
        const size = 200; // 使用固定尺寸，确保所有头像大小一致

        // 使用iOS兼容的Canvas创建方法
        const canvasResult = createIOSCompatibleCanvas(size);
        if (!canvasResult) {
          reject(new Error('无法创建Canvas或获取上下文'));
          return;
        }

        const { canvas, ctx } = canvasResult;

        // 创建圆形遮罩
        const radius = size / 2;
        const centerX = size / 2;
        const centerY = size / 2;

        // 清除画布并设置透明背景
        ctx.clearRect(0, 0, size, size);

        // 保存当前状态
        ctx.save();

        // 创建圆形裁剪路径 - iOS Safari优化
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI, false);
        ctx.closePath();
        ctx.clip();

        // 在圆形裁剪区域内填充白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, size, size);

        // 计算图片缩放和居中位置，确保图片完全填充圆形区域
        const scale = Math.max(size / img.width, size / img.height);
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;
        const offsetX = (size - scaledWidth) / 2;
        const offsetY = (size - scaledHeight) / 2;

        // 在圆形裁剪区域内绘制缩放后的图片
        ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);

        // 恢复状态
        ctx.restore();

        // 在圆形外添加一个细边框（可选）
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius - 1, 0, 2 * Math.PI, false);
        ctx.closePath();
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 2;
        ctx.stroke();

        // 使用iOS兼容的Canvas转Base64方法
        const processedImageUrl = canvasToDataURL(canvas, imageUrl);

        console.log('🎨 图片处理完成:', {
          原始尺寸: `${img.width}x${img.height}`,
          处理后尺寸: `${size}x${size}`,
          缩放比例: scale.toFixed(2),
          iOS设备: isIOSDevice(),
          'iOS Safari': isIOSSafari(),
          Canvas尺寸: `${canvas.width}x${canvas.height}`,
        });
        resolve(processedImageUrl);
      } catch (error) {
        console.error('图片处理过程中出错:', error);
        reject(error);
      }
    };

    const handleImageError = (error: Event | string) => {
      clearTimeout(loadTimeout);
      console.error('🚫 图片加载失败:', { imageUrl, error });
      reject(new Error(`图片加载失败: ${imageUrl}`));
    };

    img.onload = handleImageLoad;
    img.onerror = handleImageError;

    // 添加加载开始的日志
    console.log('🖼️ 开始处理图片:', {
      url: imageUrl,
      iOS设备: isIOSDevice(),
      iOS_Safari: isIOSSafari(),
      设备像素比: window.devicePixelRatio,
    });

    img.src = imageUrl;
  });
};

/**
 * 批量处理多个头像图片
 * @param imageUrls 图片URL数组
 * @returns Promise<string[]> 处理后的图片URL数组
 */
export const processBatchAvatarImages = async (imageUrls: string[]): Promise<string[]> => {
  const promises = imageUrls.map((url) => processAvatarImage(url));
  return Promise.all(promises);
};

/**
 * 检查图片是否需要预处理
 * 如果图片已经是圆形或者是base64格式，可能不需要重复处理
 * @param imageUrl 图片URL
 * @returns boolean 是否需要预处理
 */
export const shouldProcessImage = (imageUrl: string): boolean => {
  // 如果已经是我们处理过的base64格式，不需要重复处理
  if (imageUrl.startsWith('data:image/png;base64,')) {
    return false;
  }

  // 如果是其他base64格式，仍然需要处理以确保圆形
  // 所有其他情况都需要处理成圆形
  return true;
};
