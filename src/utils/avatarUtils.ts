// 导入默认头像
import avatar1 from '@/assets/icon/user_avatar1.png';
import avatar2 from '@/assets/icon/user_avatar2.png';
import avatar3 from '@/assets/icon/user_avatar3.png';
import avatar4 from '@/assets/icon/user_avatar4.png';
import avatar5 from '@/assets/icon/user_avatar5.png';
import avatar6 from '@/assets/icon/user_avatar6.png';
import avatar7 from '@/assets/icon/user_avatar7.png';
import avatar8 from '@/assets/icon/user_avatar8.png';
import defaultAvatar from '@/assets/icon/user_avatar.png';

// 默认头像映射
const defaultAvatarMap: Record<string, string> = {
  default_avatar_1: avatar1,
  default_avatar_2: avatar2,
  default_avatar_3: avatar3,
  default_avatar_4: avatar4,
  default_avatar_5: avatar5,
  default_avatar_6: avatar6,
  default_avatar_7: avatar7,
  default_avatar_8: avatar8,
};

/**
 * 根据人员ID获取随机默认头像ID
 * @param personId 人员ID，用于确保同一人员总是获得相同的头像
 * @returns 默认头像ID
 */
export function getRandomDefaultAvatarId(personId: string): string {
  const avatarIds = Object.keys(defaultAvatarMap);
  // 使用人员ID的简单哈希值来确保同一人员总是获得相同的头像
  let hash = 0;
  for (let i = 0; i < personId.length; i++) {
    const char = personId.charCodeAt(i);
    hash = (hash * 31 + char) % 1000000; // 使用简单的乘法和取模运算
  }
  const index = Math.abs(hash) % avatarIds.length;
  return avatarIds[index];
}

/**
 * 根据头像标识符获取头像URL
 * @param avatarId 头像标识符，可能是默认头像ID或自定义头像URL
 * @param personId 人员ID，当avatarId为空时用于生成随机默认头像
 * @returns 头像URL
 */
export function getAvatarUrl(avatarId: string | null | undefined, personId?: string): string {
  if (!avatarId) {
    // 如果没有头像且有人员ID，返回随机默认头像
    if (personId) {
      const randomAvatarId = getRandomDefaultAvatarId(personId);
      return defaultAvatarMap[randomAvatarId];
    }
    return defaultAvatar;
  }

  // 如果是默认头像ID，返回对应的本地图片
  if (defaultAvatarMap[avatarId]) {
    return defaultAvatarMap[avatarId];
  }

  // 如果是自定义头像URL，直接返回
  if (avatarId.startsWith('http') || avatarId.startsWith('data:') || avatarId.startsWith('blob:')) {
    return avatarId;
  }

  // 其他情况返回默认头像
  return defaultAvatar;
}

/**
 * 检查是否为默认头像
 * @param avatarId 头像标识符
 * @returns 是否为默认头像
 */
export function isDefaultAvatar(avatarId: string | null | undefined): boolean {
  return !!(avatarId && defaultAvatarMap[avatarId]);
}

/**
 * 获取所有默认头像列表
 * @returns 默认头像列表
 */
export function getDefaultAvatars() {
  return [
    { id: 'default_avatar_1', src: avatar1 },
    { id: 'default_avatar_2', src: avatar2 },
    { id: 'default_avatar_3', src: avatar3 },
    { id: 'default_avatar_4', src: avatar4 },
    { id: 'default_avatar_5', src: avatar5 },
    { id: 'default_avatar_6', src: avatar6 },
    { id: 'default_avatar_7', src: avatar7 },
    { id: 'default_avatar_8', src: avatar8 },
  ];
}
