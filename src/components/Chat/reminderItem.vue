<template>
  <div class="reminder-item" @click="handleClick">
    <div class="reminder-content">
      <div class="reminder-text">
        {{ reminderData.display_text || '暂无提醒内容' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type IReminder } from '@/apis/memory';

interface IProps {
  reminderData: IReminder;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  click: [reminder: IReminder];
}>();

const handleClick = () => {
  emit('click', props.reminderData);
};
</script>

<style scoped lang="scss">
.reminder-item {
  width: 220px;
  height: 220px;
  background: rgba(1, 28, 32, 0.4);
  border: none;
  border-radius: 16px;
  padding: 20px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
      -6px 0 16px rgba(0, 255, 255, 0.4),
      0 12px 32px rgba(0, 0, 0, 0.3);
    border-left-color: #00ffff;
    background: rgba(1, 28, 32, 0.8);
  }

  &:active {
    transform: translateY(-4px) scale(1.02);
  }
}

.reminder-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
}

.reminder-text {
  flex: 1;
  color: rgba(255, 255, 255, 0.9);
  font-size: 26px; // 增加4px (原来14px)
  font-weight: 500;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-break: break-word;
  text-overflow: ellipsis;
}
</style>
