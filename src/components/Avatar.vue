<script lang="ts" setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  size: {
    type: Number,
    default: 30,
  },
  shape: {
    type: String,
    values: ['circle', 'square'],
    default: 'circle',
  },
  src: {
    type: String,
    default: '',
  },
  alt: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['error']);

const borderRadius = computed(() => {
  return props.shape === 'circle' ? '50%' : '8px';
});

const width = computed(() => {
  return `${props.size}px`;
});

const hasLoadError = ref(false);

// need reset hasLoadError to false if src changed
watch(
  () => props.src,
  () => {
    hasLoadError.value = false;
  },
);

function handleError(e: Event) {
  hasLoadError.value = true;
  emit('error', e);
}
</script>

<template>
  <span class="custom-avatar">
    <img v-if="props.src && !hasLoadError" :src="props.src" :alt="props.alt" @error="handleError" />
    <slot v-else></slot>
  </span>
</template>

<style lang="scss" scoped>
.custom-avatar {
  display: inline-block;
  width: v-bind(width);
  height: v-bind(width);
  line-height: v-bind(width);
  overflow: hidden;
  vertical-align: middle;
  border-radius: v-bind(borderRadius);
  & > img,
  :slotted(img) {
    display: block;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}
</style>
