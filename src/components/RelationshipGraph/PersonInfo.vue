<template>
  <div class="person-info-container">
    <div class="person-info-card">
      <div class="person-avatar">
        <img
          v-if="personData?.avatar || personData?.image"
          :src="personData?.avatar || personData?.image"
          :alt="personData.label"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder" :style="{ backgroundColor: getTypeColor(personData?.type) }">
          {{ getAvatarText(personData?.label) }}
        </div>
      </div>

      <div class="person-details">
        <h3 class="person-name">{{ personData?.label || '未选择' }}</h3>
        <div class="person-type">
          <span class="type-badge" :style="{ backgroundColor: getTypeColor(personData?.type) }">
            {{ getTypeLabel(personData?.type) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 定义节点数据类型
interface INode {
  id: string;
  label: string;
  type: 'core' | 'friend' | 'colleague' | 'classmate' | 'family';
  image?: string;
  avatar?: string; // 添加avatar字段
}

// Props定义
interface IProps {
  personData?: INode | null;
}

const props = withDefaults(defineProps<IProps>(), {
  personData: null,
});

// 节点类型配置
const nodeTypeConfig = {
  core: {
    color: '#ff4757',
    label: '核心人物',
  },
  friend: {
    color: '#ff6b9d',
    label: '朋友',
  },
  colleague: {
    color: '#00d2d3',
    label: '同事',
  },
  classmate: {
    color: '#5352ed',
    label: '同学',
  },
  family: {
    color: '#ffa502',
    label: '家人',
  },
};

// 获取类型颜色
const getTypeColor = (type?: string) => {
  if (!type || !(type in nodeTypeConfig)) return '#ccc';
  return nodeTypeConfig[type as keyof typeof nodeTypeConfig].color;
};

// 获取类型标签
const getTypeLabel = (type?: string) => {
  if (!type || !(type in nodeTypeConfig)) return '未知';
  return nodeTypeConfig[type as keyof typeof nodeTypeConfig].label;
};

// 获取头像文字（取姓名首字符）
const getAvatarText = (label?: string) => {
  if (!label) return '?';
  return label.charAt(0);
};
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.person-info-container {
  width: 100%;
  height: 200px;
  padding: var(--spacing-xl);
  background: var(--bg-glass);
  border-top: 1px solid var(--border-accent);
  flex-shrink: 0;
  z-index: 10;
  position: relative;
  box-sizing: border-box;
}

.person-info-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  height: 140px;
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  transition: all 0.3s ease;
  box-sizing: border-box;
  backdrop-filter: blur(20px);

  &:hover {
    background: var(--bg-glass-hover);
    transform: translateY(-2px);
  }
}

.person-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  border: 3px solid var(--accent-color);
  box-shadow: var(--shadow-medium);

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
    font-weight: bold;
    background: var(--accent-color-medium);
  }
}

.person-details {
  flex: 1;
  min-width: 0;
}

.person-name {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.person-type {
  .type-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .person-info-container {
    padding: 12px;
  }

  .person-info-card {
    padding: 12px;
    gap: 12px;
  }

  .person-avatar {
    width: 48px;
    height: 48px;

    .avatar-placeholder {
      font-size: 20px;
    }
  }

  .person-name {
    font-size: 16px;
  }
}
</style>
